import requests
import re
import time
import json
from datetime import datetime, timezone, timedelta
from urllib.parse import urlparse, parse_qs
import random
import string
import threading
from concurrent.futures import ThreadPoolExecutor, as_completed


# ---- 工具函数：北京时间格式化 ----
def to_beijing_time(timestamp_ms: int) -> str:
    try:
        beijing_tz = timezone(timedelta(hours=8))
        dt = datetime.fromtimestamp(timestamp_ms / 1000, tz=beijing_tz)
        return dt.strftime('%Y-%m-%d %H:%M:%S (北京时间)')
    except Exception:
        return '时间转换失败'

# ---- 从 VIP 链接中提取 token/recordId/activityId ----
def extract_token_info(vip_url: str):
    try:
        parsed = urlparse(vip_url)
        query_params = parse_qs(parsed.query)

        token = query_params.get('token', [None])[0]
        record_id = query_params.get('recordId', [None])[0]

        # 从路径中尽力提取活动ID（与原实现一致的策略）
        path_parts = parsed.path.split('/')
        activity_id = None
        for part in path_parts:
            if part and part not in ['g', 'vip-invite-cashier']:
                activity_id = part
                break

        return {
            'token': token,
            'record_id': record_id,
            'activity_id': activity_id,
            'full_url': vip_url
        }
    except Exception as e:
        print(f"[❌ URL解析失败] {e}")
        return None

# ---- 通过 API 检查 VIP 状态（优先方式）----
def check_vip_api(token_info: dict):
    try:
        api_urls = [
            'https://interface.music.163.com/api/vipactivity/app/vip/invitation/detail/info/get',
            'https://interface.music.163.com/api/vip/invitation/detail',
            'https://music.163.com/api/vip/invitation/detail'
        ]

        for api_url in api_urls:
            try:
                params = {}
                if token_info.get('token'):
                    params['token'] = token_info['token']
                if token_info.get('record_id'):
                    params['recordId'] = token_info['record_id']

                # print(f"[🔍 尝试API] {api_url}")
                response = requests.get(api_url, params=params, timeout=10)
                if response.status_code == 200:
                    try:
                        data = response.json()
                        if 'data' in data:
                            detail_data = data['data']
                            # 兼容多种字段名
                            expire_time = (detail_data.get('expireTime') or
                                           detail_data.get('tokenExpireTime') or
                                           detail_data.get('expire_time') or
                                           detail_data.get('token_expire_time'))
                            if expire_time:
                                current_time = int(time.time() * 1000)
                                is_valid = expire_time > current_time
                                expire_date_beijing = to_beijing_time(expire_time)
                                remaining_days = (expire_time - current_time) / (1000 * 60 * 60 * 24)

                                return {
                                    'is_valid': is_valid,
                                    'expire_time': expire_time,
                                    'expire_date': expire_date_beijing,
                                    'remaining_days': remaining_days,
                                    'api_data': detail_data,
                                    'method': 'api',
                                    'error': None
                                }
                    except json.JSONDecodeError:
                        # print(f"[⚠️ JSON解析失败] {response.text[:200]}")
                        pass
            except Exception:
                # 切换下一个 API
                continue

        return None
    except Exception as e:
        # print(f"[❌ API检查失败] {str(e)}")
        return None

# ---- 页面解析回退：从 HTML 中提取过期时间 ----
def check_vip_expiry_by_page(redirect_url: str):
    try:
        resp = requests.get(redirect_url, timeout=15)
        resp.raise_for_status()
        content = resp.text

        # 多模式正则匹配 13 位时间戳（与 UI 版一致/相近）
        expire_patterns = [
            r'["\']?expireTime["\']?\s*:\s*(\d{13})',
            r'expireTime["\']?\s*=\s*(\d{13})',
            r'expire[^:]*:\s*(\d{13})',
            r'time[^:]*:\s*(\d{13})',
            r'expireTime["\']?\s*[=:]\s*["\']?(\d{13})["\']?',
            r'expire_time["\']?\s*[=:]\s*["\']?(\d{13})["\']?',
            r'"expireTime"\s*:\s*(\d{13})',
            r"'expireTime'\s*:\s*(\d{13})",
            r'tokenExpireTime["\']?\s*[=:]\s*["\']?(\d{13})["\']?',
        ]

        expire_time = None
        for pattern in expire_patterns:
            matches = re.findall(pattern, content, re.IGNORECASE)
            for m in matches:
                if isinstance(m, str) and m.isdigit() and len(m) == 13:
                    expire_time = int(m)
                    break
            if expire_time:
                break

        # 通用兜底：抓取所有 13 位时间戳，取未来最大值
        if not expire_time:
            timestamp_pattern = r'\b(1[6-9]\d{11})\b'
            timestamps = re.findall(timestamp_pattern, content)
            if timestamps:
                current_time = int(time.time() * 1000)
                future_timestamps = [int(ts) for ts in timestamps if int(ts) > current_time]
                if future_timestamps:
                    expire_time = max(future_timestamps)
                else:
                    expire_time = max(int(ts) for ts in timestamps)  # 全部过去，取最大

        if expire_time:
            current_time = int(time.time() * 1000)
            is_valid = expire_time > current_time
            expire_date_beijing = to_beijing_time(expire_time)
            remaining_days = (expire_time - current_time) / (1000 * 60 * 60 * 24)

            return {
                'is_valid': is_valid,
                'expire_time': expire_time,
                'expire_date': expire_date_beijing,
                'remaining_days': remaining_days,
                'method': 'page',
                'error': None
            }

        # 页面提示兜底（中文提示词）
        status_indicators = {
            '已过期': 'expired',
            '活动已结束': 'ended',
            '邀请已失效': 'invalid',
            '链接已失效': 'invalid',
        }
        for indicator, status in status_indicators.items():
            if indicator in content:
                return {
                    'is_valid': False,
                    'expire_time': None,
                    'expire_date': None,
                    'remaining_days': 0,
                    'method': 'page',
                    'error': f'页面状态: {status} ({indicator})'
                }

        return {
            'is_valid': False,
            'expire_time': None,
            'expire_date': None,
            'remaining_days': 0,
            'method': 'page',
            'error': '未找到有效期信息'
        }
    except requests.RequestException as e:
        return {
            'is_valid': False,
            'expire_time': None,
            'expire_date': None,
            'remaining_days': 0,
            'method': 'error',
            'error': f'网络请求失败: {str(e)}'
        }
    except Exception as e:
        return {
            'is_valid': False,
            'expire_time': None,
            'expire_date': None,
            'remaining_days': 0,
            'method': 'error',
            'error': f'解析失败: {str(e)}'
        }

# ---- 统一检查：优先 API，失败回退页面解析 ----
def check_vip_expiry(redirect_url: str):
    token_info = extract_token_info(redirect_url)
    if token_info and token_info.get('token'):
        api_result = check_vip_api(token_info)
        if api_result:
            return api_result
    # API 失败，回退页面解析
    return check_vip_expiry_by_page(redirect_url)

# ---- 入口：传入短链接，自动判断是否 VIP 并给出结果 ----
def analyze_short_link(short_url: str) -> dict:
    try:
        # 优先 HEAD 获取重定向
        try:
            r = requests.head(short_url, allow_redirects=False, timeout=5)
            if r.status_code in (301, 302, 303, 307, 308) and 'Location' in r.headers:
                redirect_url = r.headers['Location']
            else:
                # HEAD 不行就 GET
                rg = requests.get(short_url, allow_redirects=True, timeout=10)
                redirect_url = rg.url
        except Exception:
            rg = requests.get(short_url, allow_redirects=True, timeout=10)
            redirect_url = rg.url

        is_vip_link = 'vip-invite-cashier' in (redirect_url or '')
        if not is_vip_link:
            return {
                'status': 'success',
                'short_url': short_url,
                'redirect_url': redirect_url,
                'is_vip_link': False,
                'vip_status': 'not_vip',
                'vip_status_text': '非VIP链接',
                'expire_time': None,
                'expire_date': None,
                'remaining_days': 0,
                'method': None,
                'error': None
            }

        # VIP 链接：检查有效期
        expiry_result = check_vip_expiry(redirect_url)

        if expiry_result.get('error'):
            vip_status = 'expiry_check_failed'
            vip_status_text = f"有效期检查失败: {expiry_result['error']}"
        elif expiry_result.get('is_valid') is False:
            vip_status = 'expired'
            vip_status_text = f"VIP已过期 (过期时间: {expiry_result.get('expire_date', 'Unknown')})"
        else:
            vip_status = 'valid'
            vip_status_text = f"VIP有效 (剩余: {expiry_result.get('remaining_days', 0):.1f}天)"

        result = {
            'status': 'success',
            'short_url': short_url,
            'redirect_url': redirect_url,
            'is_vip_link': True,
            'vip_status': vip_status,
            'vip_status_text': vip_status_text,
            'expire_time': expiry_result.get('expire_time'),
            'expire_date': expiry_result.get('expire_date'),
            'remaining_days': expiry_result.get('remaining_days'),
            'method': expiry_result.get('method'),
            'error': expiry_result.get('error')
        }

        # 如果 API 返回了邀请者/总天数等信息，也一并带上（可选）
        api_data = expiry_result.get('api_data') or {}
        if api_data:
            inviter = api_data.get('inviter', {})
            if isinstance(inviter, dict) and 'nickname' in inviter:
                result['sender'] = inviter['nickname']
            elif 'inviterNickname' in api_data:
                result['sender'] = api_data['inviterNickname']

            if 'inviterTotalDays' in api_data:
                result['gift_type'] = f"VIP邀请 ({api_data['inviterTotalDays']}天)"
                result['gift_count'] = f"{api_data['inviterTotalDays']}天"
            elif 'totalDays' in api_data:
                result['gift_type'] = f"VIP邀请 ({api_data['totalDays']}天)"
                result['gift_count'] = f"{api_data['totalDays']}天"

        return result

    except Exception as e:
        return {
            'status': 'error',
            'message': f'分析失败: {str(e)}',
            'short_url': short_url,
            'is_vip_link': False
        }

# ==================== 短链接生成与并发扫描（合并自 test.py，按新规则修改） ====================
# 规则：开头固定 'G'，后接 7 位 [A-Za-z0-9]，且这 7 位中至少包含 1 个数字
SHORT_BASE = 'http://163cn.tv/'  # 网易云短域名
CODE_TAIL_CHARSET = string.ascii_letters + string.digits


def generate_netease_code() -> str:
    """生成符合规则的短码：'G' + 7位 [A-Za-z0-9]，且至少包含1个数字、1个大写字母、1个小写字母"""
    while True:
        tail = ''.join(random.choices(CODE_TAIL_CHARSET, k=6))
        has_digit = any(ch.isdigit() for ch in tail)
        has_upper = any(ch.isupper() for ch in tail)
        has_lower = any(ch.islower() for ch in tail)
        if has_digit and has_upper and has_lower:
            return 'I' + tail


def build_short_url(code: str) -> str:
    return SHORT_BASE + code


def should_save(expire_time_ms: int, today_beijing: datetime, current_year: int) -> bool:
    """保存条件：
    - 链接有效（由上游保证）
    - 过期时间 >= 今天(北京时区) 的 00:00:00，或 过期年份 > 当前年份
    """
    if not expire_time_ms:
        return False
    beijing_tz = timezone(timedelta(hours=8))
    expire_dt = datetime.fromtimestamp(expire_time_ms / 1000, tz=beijing_tz)
    if expire_dt >= today_beijing:
        return True
    if expire_dt.year > current_year:
        return True
    return False


def process_code(code: str, lock: threading.Lock, outfile_path: str, today_beijing: datetime, current_year: int) -> tuple:
    """检查单个短码；满足条件则实时写入文件。
    返回 (code, saved: bool, message: str)
    """
    short_url = build_short_url(code)
    res = analyze_short_link(short_url)
    if res.get('status') != 'success' or not res.get('is_vip_link'):
        return code, False, '非VIP或分析失败'

    if res.get('vip_status') != 'valid':
        return code, False, res.get('vip_status_text', '无效')

    expire_time = res.get('expire_time')
    if should_save(expire_time, today_beijing, current_year):
        line = short_url + '  |  过期: ' + (res.get('expire_date') or '') + '\n'
        with lock:
            with open(outfile_path, 'a', encoding='utf-8') as f:
                f.write(line)
                f.flush()
        return code, True, '已保存'
    else:
        return code, False, '到期时间不满足要求'

# http://163cn.tv/GeWwm9KC
# http://163cn.tv/GKBEW3e
# https://163cn.tv/IMrfeNq
def scan_and_save_valid_invites(
    count: int = 10000,
    threads: int = 30,
    output_file: str = 'valid_vip_links.txt',
    seed: int | None = None,
):
    """并发扫描随机短码，实时保存满足条件的有效VIP邀请短链。

    参数：
    - count: 生成并检查的短码数量
    - threads: 并发线程数
    - output_file: 实时追加保存的文件
    - seed: 随机种子（可复现实验）
    """
    if seed is not None:
        random.seed(seed)

    # “今天”为北京时区的当天零点；根据用户要求固定为 2025-08-26
    beijing_tz = timezone(timedelta(hours=8))
    today_beijing = datetime(2025, 8, 26, 0, 0, 0, tzinfo=beijing_tz)
    current_year = 2025

    lock = threading.Lock()
    seen_codes: set[str] = set()

    print(f"📌 扫描数量: {count} | 线程: {threads} | 输出文件: {output_file}")
    saved = 0

    def task_one(idx: int):
        # 确保每个任务处理唯一短码
        while True:
            code = generate_netease_code()
            with lock:
                if code in seen_codes:
                    continue
                seen_codes.add(code)
                break
        c, ok, msg = process_code(code, lock, output_file, today_beijing, current_year)
        return idx, c, ok, msg

    with ThreadPoolExecutor(max_workers=threads) as executor:
        futures = {executor.submit(task_one, i): i for i in range(1, count + 1)}
        for i, fut in enumerate(as_completed(futures), 1):
            try:
                idx, code, ok, msg = fut.result()
                if ok:
                    saved += 1
                    print(f"[{i}/{count}] {code} ✅ {msg} | 已保存: {saved}")
                else:
                    print(f"[{i}/{count}] {code} ❌ {msg}")
            except Exception as e:
                print(f"[{i}/{count}] ⚠️ 任务异常: {e}")

    print(f"\n🎉 完成：共保存 {saved} 条到 {output_file}")

if __name__ == "__main__":
    # 扫描参数（可按需修改）
    scan_and_save_valid_invites(count=1000, threads=30, output_file='valid_vip_links.txt', seed=123456)
